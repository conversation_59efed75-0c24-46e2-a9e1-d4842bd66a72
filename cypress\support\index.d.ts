// Global Cypress declarations with comprehensive type definitions
import type {
  GraphQLResponse,
  CypressResponse,
  LoginResponse,
  FetchProfilesResponse,
  DeleteProfileResponse,
  FetchRedactionCodeResponse,
  DeleteRedactionCodeResponse,
  TDODownloadResponse,
  TDODetailResponse,
  UpdateSettingsTDOResponse,
  GraphQLVariables,
} from './types/graphql';

import type {
  UDRCoordinates,
  RepeatOptions,
  NetworkResponseOptions,
  DataIdCyOptions,
  DataSetCyOptions,
  NavigationOptions,
  FileUploadOptions,
  KeyValueOptions,
  FilterByAriaOptions,
  CSSPropertyOptions,
  VideoControlOptions,
  InterpolationValues,
  WindowWithStore,
} from './types/commands';

declare global {
  const cy: Cypress.cy;
  const Cypress: Cypress.Cypress;
  const expect: Chai.ExpectStatic;

  interface Window extends WindowWithStore {}

  namespace Cypress {
    interface Chainable {
      // Authentication Commands
      /**
       * Login to the application
       */
      LoginToApp(): Chainable<Cypress.Response<LoginResponse>>;

      /**
       * Login and navigate to landing page
       */
      LoginLandingPage(): Chainable<void>;

      // Navigation Commands
      /**
       * Navigate to a section by name
       */
      navigateToSectionByName(options: NavigationOptions): Chainable<void>;

      /**
       * Go to test file by name
       */
      GoToTestFile(name: string): Chainable<void>;

      /**
       * Reset and go to test file
       */
      ResetAndGoToTestFile(name: string): Chainable<void>;

      // Element Selection Commands
      /**
       * Get element by data-test-id with options
       */
      getDataIdCy(options: DataIdCyOptions): Chainable<JQuery<HTMLElement>>;

      /**
       * Get element by data-test attribute
       */
      getDataSetCy(options: DataSetCyOptions): Chainable<JQuery<HTMLElement>>;

      /**
       * Get elements by role
       */
      getByRoles(role: string): Chainable<JQuery<HTMLElement>>;

      // GraphQL Commands
      /**
       * Execute GraphQL query with proper typing
       */
      Graphql<T = unknown>(query: string, variables?: GraphQLVariables): Chainable<CypressResponse<T>>;

      /**
       * Intercept GraphQL queries
       */
      interceptGraphQLQuery(query: string, alias: string): Chainable<void>;

      // File Operations
      /**
       * Open upload file modal
       */
      OpenUploadFileModal(): Chainable<void>;

      /**
       * Select file for upload
       */
      SelectFile(options: FileUploadOptions): Chainable<void>;

      /**
       * Click to upload file
       */
      ClickToUpload(): Chainable<void>;

      // UDR (User Defined Region) Commands
      /**
       * Delete UDR groups
       */
      DeleteUDRGroups(): Chainable<void>;

      /**
       * Draw custom UDRs with increasing coordinates
       */
      DrawCustomUDRsWithIncreasingCoordinates(count: number): Chainable<void>;

      /**
       * Draw a single UDR
       */
      DrawAnUdr(coordinates: UDRCoordinates): Chainable<void>;

      // Media Player Commands
      /**
       * Stop video at specific seconds
       */
      StopVideoAtSeconds(seconds: number): Chainable<void>;

      // Utility Commands
      /**
       * Repeat an action multiple times
       */
      repeat(options: RepeatOptions): Chainable<void>;

      /**
       * Wait for network response with specific code
       */
      awaitNetworkResponseCode(options: NetworkResponseOptions): Chainable<void>;

      /**
       * Assert no loading indicators are present
       */
      assertNoLoading(): Chainable<void>;

      // Key-Value Storage Commands
      /**
       * Set key-value pair
       */
      setKeyValue(keyName: string, value: string | number): Chainable<void>;

      /**
       * Get value by key
       */
      getKeyValue(keyName: string): Chainable<string | number>;

      // State Management Commands
      /**
       * Get Redux store state
       */
      getState(): Chainable<unknown>;

      // Profile Management Commands
      /**
       * Delete profile by name
       */
      deleteProfileByName(name: string): Chainable<void>;

      // Redaction Code Commands
      /**
       * Delete redaction code if it exists
       */
      deleteRedactionCodeIfExist(redactionCodeName: string): Chainable<void>;

      // TDO Management Commands
      /**
       * Delete TDO by name list
       */
      deleteTdoByName(nameList: string[]): Chainable<void>;

      /**
       * Delete all redacted assets
       */
      deleteAllRedacted(tdoId: string): Chainable<void>;

      /**
       * Clear trim range for TDO
       */
      clearTrimRange(tdoId: string): Chainable<void>;

      // CSS and Styling Commands
      /**
       * Assert element has CSS property
       */
      toHaveCssProperty(attr: string, value?: string): Chainable<JQuery<HTMLElement>>;

      /**
       * Assert element does not have CSS property
       */
      toNotHaveCssProperty(attr: string, value?: string): Chainable<JQuery<HTMLElement>>;

      /**
       * Filter elements by aria attribute
       */
      filterByAria(attr: string, value: string): Chainable<JQuery<HTMLElement>>;
    }
  }
}

export {};
