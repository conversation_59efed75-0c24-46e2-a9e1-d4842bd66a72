// Cypress Command Types

export interface UDRCoordinates {
  orderNumber: number;
  x1: number;
  y1: number;
  x2: number;
  y2: number;
}

export interface RepeatOptions {
  action: () => void;
  times: number;
}

export interface NetworkResponseOptions {
  alias: string;
  code: number;
  repeat?: number;
}

export interface DataIdCyOptions {
  idAlias: string;
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
}

export interface DataSetCyOptions {
  cyAlias: string;
  options?: Partial<Cypress.Loggable & Cypress.Timeoutable & Cypress.Shadow>;
}

export interface NavigationOptions {
  sectionName: string;
}

export interface FileUploadOptions {
  videoName: string;
}

export interface FileAttachmentOptions {
  fileContent: Blob;
  fileName: string;
  mimeType: string;
  encoding: string;
}

export interface KeyValueOptions {
  key: string;
  value: string | number;
}

export interface FilterByAriaOptions {
  attr: string;
  value: string;
}

// Media Player Types
export interface MediaPlayerState {
  currentTime: number;
  duration: number;
  paused: boolean;
  volume: number;
}

// Store State Types (for Redux store access)
export interface StoreState {
  [key: string]: unknown;
}

// Window with Store
export interface WindowWithStore extends Window {
  store: {
    getState: () => StoreState;
    dispatch: (action: unknown) => void;
  };
}

// File Upload Types
export interface UploadFileContent {
  fileContent: Blob;
  fileName: string;
  mimeType: string;
  encoding?: string;
}

// GraphQL Variables
export interface GraphQLVariables {
  [key: string]: unknown;
}

// Custom Cypress Types for better type safety
export type CypressChainable<T = unknown> = Cypress.Chainable<T>;
export type CypressElement = Cypress.Chainable<JQuery<HTMLElement>>;
export type CypressSubject = JQuery<HTMLElement>;

// Task Types for Cypress tasks
export interface CypressTaskOptions {
  [key: string]: unknown;
}

// Intercept Types
export interface GraphQLInterceptRequest {
  body: {
    query: string;
    variables?: GraphQLVariables;
  };
  url: string;
  alias?: string;
  continue: () => void;
}

// CSS Property Types
export interface CSSPropertyOptions {
  attr: string;
  value?: string;
}

// Role-based selector types
export interface RoleOptions {
  role: string;
  filters?: string;
}

// Video control types
export interface VideoControlOptions {
  seconds: number;
}

// Drawing UDR types
export interface DrawUDROptions extends UDRCoordinates {
  // Additional options for drawing UDRs
}

// Custom command return types
export type LoginResult = Cypress.Chainable<Cypress.Response<unknown>>;
export type GraphQLResult<T = unknown> = Cypress.Chainable<Cypress.Response<T>>;
export type ElementResult = Cypress.Chainable<JQuery<HTMLElement>>;
export type VoidResult = Cypress.Chainable<void>;

// Assertion types for custom assertions
export interface CustomAssertions {
  toHaveCssProperty: (attr: string, value?: string) => void;
  toNotHaveCssProperty: (attr: string, value?: string) => void;
  filterByAria: (attr: string, value: string) => ElementResult;
}

// Environment variable types
export interface CypressEnv {
  apiRoot: string;
  username: string;
  password: string;
  token?: string;
  userId?: string;
  orgId?: string;
}

// Fixture types
export interface FixtureData {
  [key: string]: unknown;
}

// Test context types
export interface TestContext {
  role?: string;
  [key: string]: unknown;
}

// Wait options for jobs
export interface WaitForJobOptions {
  maxRetryAttempts: number;
  retryCount?: number;
  _targetId?: JQuery<HTMLElement>;
}

// Interpolation values
export interface InterpolationValues {
  auto: string | number;
  manual: string | number;
}

// Time range types
export interface TimeRange {
  start: number;
  end: number;
}

// Video result types
export interface VideoResult {
  success: boolean;
  message?: string;
}

// Redaction effect types
export type RedactionEffect = 'blur' | 'pixelate' | 'blackout' | 'custom';

// Data test selector types
export interface DataTestSelector {
  [key: string]: string;
}

// Color types for UI testing
export interface Colors {
  [colorName: string]: string;
}

// CSS element types
export interface CSSElement {
  [property: string]: string;
}
