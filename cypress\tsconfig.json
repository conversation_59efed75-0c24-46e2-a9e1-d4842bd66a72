{
  "compilerOptions": {
    "target": "es2023",
    "lib": ["es2023", "dom"],
    "module": "ESNext",
    "moduleResolution": "node",
    "baseUrl": "./",
    "skipLibCheck": true,
    "noImplicitAny": false,
    "strict": false,
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "types": ["cypress", "node", "chai"],
    "typeRoots": [
      "../node_modules/@types",
      "../node_modules/cypress/types"
    ],

  },
  "include": [
    "**/*.js",
    "**/*.ts",
    "**/*.tsx",
    "support/index.d.ts"
  ],
  "exclude": [
    "node_modules",
    "../src",
    "../test",
    "../build",
    "../dist"
  ]
}
