{"extends": "../tsconfig.json", "compilerOptions": {"target": "es2023", "lib": ["es2023", "dom"], "module": "ESNext", "moduleResolution": "node", "baseUrl": "./", "types": ["cypress", "node", "chai"], "typeRoots": ["../node_modules/@types", "../node_modules/cypress/types", "../types/", "./support/types"], "paths": {"@cbsa/*": ["../src/cbsa-app/*"], "@common/*": ["../src/common-app/*"], "@redact/*": ["../src/redact-app/*"], "@resources/*": ["../resources/*"], "@helpers/*": ["../src/helpers/*"], "@utils": ["../src/utils/index"]}}, "include": ["**/*.js", "**/*.ts", "**/*.tsx", "support/index.d.ts", "support/types/**/*"], "exclude": ["node_modules", "../src", "../test", "../build", "../dist"]}